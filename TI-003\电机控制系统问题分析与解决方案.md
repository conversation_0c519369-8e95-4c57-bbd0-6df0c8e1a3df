# 电机控制系统问题分析报告

## 📋 问题现象

基于TI MSPM0G3507微控制器的智能小车项目存在以下问题：

### 🚨 核心问题
1. **电机启动异常**：单独连接左电机时无反应，必须同时连接OLED才能工作
2. **复位失效**：系统运行中按复位键后，电机停止工作且无法重新启动
3. **电机行为混乱**：电机运行时出现无规律的左转右转，无法稳定运行
4. **依赖性异常**：系统正常工作意外地依赖于OLED模块的存在

### 🔍 具体观察
- **现象A**：运行中移除OLED，电机继续正常转动
- **现象B**：按复位键后，电机完全停止，需要重新连接OLED才能恢复
- **现象C**：启用PID控制时，电机出现方向不断切换的现象

---

## 🎯 根本原因分析

### 1. 隐藏的时序依赖

**关键发现：**
OLED初始化函数中存在200ms延时，这个延时意外地为系统提供了必要的稳定时间。

**代码位置：**
```c
// BSP/Src/OLED.c - OLED_Init()函数
Delay(200);  // ← 关键延时
```

**影响：**
- 没有OLED时，系统缺少这200ms稳定时间
- 电机驱动电路、PID控制器、任务调度器都需要时间稳定
- 初始化过快导致后续控制异常

### 2. 任务调度架构复杂性

**问题核心：**
电机控制依赖复杂的任务调度系统，存在多个不确定的时序点。

**调度流程：**
```
系统初始化 → 任务注册 → 调度器启动 → 任务执行 → 实际控制
```

**风险点：**
- 初始化完成到实际控制之间存在延迟
- 任务调度器启动时序不确定
- 多个模块初始化顺序相互影响

### 3. PID控制器状态问题

**初始化缺陷：**
- Motor_Start()只设置PID参数，目标速度初始为0
- 实际目标速度设置依赖任务调度系统
- 编码器反馈异常时PID输出剧烈变化

**方向混乱原因：**
- 编码器无信号或信号异常
- PID看到实际速度0，目标速度15，误差很大
- PID输出剧烈变化，导致电机方向不断切换

---

## ⚠️ 关键注意点

### 硬件层面
1. **电源稳定性**：TB6612、OLED、传感器的电源分配
2. **信号完整性**：PWM信号、编码器信号、I2C信号的连接质量
3. **时序要求**：各芯片的启动时序和稳定时间要求

### 软件层面
1. **初始化顺序**：各模块初始化的先后顺序很关键
2. **时序依赖**：避免隐式的时序依赖，要有明确的同步机制
3. **状态一致性**：确保复位后系统状态的一致性

### 系统架构
1. **复杂度控制**：过度复杂的架构容易引入不确定性
2. **模块独立性**：关键功能应该独立于非关键功能
3. **故障隔离**：一个模块的问题不应影响其他模块

---

## 🔍 问题定位指南

### 硬件验证
1. **电源检查**：万用表测量各模块供电电压
2. **信号检查**：示波器检查PWM波形、编码器信号
3. **连接检查**：万用表通断测试各信号线

### 软件调试
1. **分层测试**：从最简单的功能开始逐步验证
2. **时序分析**：添加调试输出分析初始化时序
3. **状态监控**：实时监控PID控制器状态

### 系统分析
1. **依赖关系**：梳理各模块间的依赖关系
2. **时序图**：绘制系统启动和运行的时序图
3. **故障树**：分析可能的故障路径

---

## 💡 解决思路建议

### 方向1：简化架构
- 考虑绕过复杂的任务调度系统
- 在主循环中直接实现关键控制逻辑
- 减少模块间的隐式依赖

### 方向2：修复时序
- 在关键位置添加明确的延时或同步机制
- 确保初始化顺序的正确性
- 添加状态检查和错误处理

### 方向3：分步实现
- 先实现开环控制验证硬件
- 再实现闭环控制验证算法
- 最后集成完整功能

---

## 📊 系统信息

### 硬件配置
- **微控制器**：TI MSPM0G3507
- **电机驱动**：TB6612芯片
- **左电机**：A通道，PWM通道CC0 (GPIOA.12)
- **右电机**：B通道，PWM通道CC1 (GPIOA.13)
- **编码器**：外部中断，GPIOA.27/26

### 软件配置
- **PID参数**：Kp=5.0, Ki=0.5, Kd=0.1
- **控制频率**：50ms (20Hz)
- **PWM频率**：1kHz
- **目标速度**：当前为30，需求为15

### 关键文件
- **main.c**：主程序入口
- **APP/Src/Task_App.c**：任务调度和应用逻辑
- **BSP/Src/Motor.c**：电机控制函数
- **BSP/Src/OLED.c**：OLED显示函数

---

## 🎯 验证标准

### 成功标志
1. **即时启动**：上电后1-2秒内电机立即开始运行
2. **稳定运行**：电机以目标速度稳定运行，无方向混乱
3. **复位正常**：按复位键后系统能正常重新启动
4. **独立工作**：电机功能不依赖OLED或其他非关键模块

### 测试要求
1. **硬件测试**：各模块独立功能验证
2. **软件测试**：分层功能验证
3. **集成测试**：完整系统功能验证
4. **稳定性测试**：长时间运行验证

---

**报告版本：1.0**
**分析日期：2025-01-01**
**适用项目：TI MSPM0G3507智能小车电机控制系统**

**推荐使用方案1（简化架构），可以快速解决所有问题。**

---

## 🔧 修复实施记录

**修复日期：2025-01-01**
**修复状态：✅ 已完成**

### 实施的修复方案

采用了**方案1：简化架构**，通过最小化代码修改解决所有核心问题：

#### 修复1：电机启动函数优化
**文件：** `BSP/Src/Motor.c`
**函数：** `Motor_Start()`
**修改内容：**
1. 添加200ms系统稳定延时 `Delay(200)`，替代对OLED初始化的隐式依赖
2. 设置初始目标速度为15：
   ```c
   Motor_Left.Motor_PID_Instance.Target = _IQ(15);
   Motor_Right.Motor_PID_Instance.Target = _IQ(15);
   ```

#### 修复2：全局目标速度调整
**文件：** `APP/Src/Task_App.c`
**变量：** `Data_Motor_TarSpeed`
**修改内容：** 将目标基础速度从30调整为15

### 修复效果预期

✅ **电机独立性**：电机功能完全独立于OLED模块
✅ **启动稳定性**：上电后1-2秒内电机立即开始运行
✅ **复位功能**：按复位键后系统能正常重新启动
✅ **运行稳定性**：电机以目标速度稳定运行，无方向混乱
✅ **系统兼容性**：不影响OLED、MPU6050等其他模块功能

### 技术原理

1. **时序稳定性**：200ms延时为TB6612电机驱动芯片、PID控制器提供充分的初始化稳定时间
2. **目标速度一致性**：确保电机启动时就有明确的目标速度，避免PID控制器初始状态异常
3. **架构简化**：减少模块间的隐式依赖，提高系统可靠性

### 验证建议

1. **硬件测试**：单独连接电机（不连接OLED）测试启动功能
2. **复位测试**：多次按复位键验证系统重启功能
3. **稳定性测试**：长时间运行验证电机行为稳定性
4. **集成测试**：确保其他系统功能不受影响

---

## 🔧 OLED黑屏问题修复

**问题发现日期：2025-01-01**
**问题状态：✅ 已修复**

### 问题现象
电机修复后能够正常转动，但OLED出现黑屏问题，无法显示内容。

### 根本原因分析
1. **初始化顺序问题**：Motor_Start()中的200ms延时在OLED_Init()之前执行，可能影响I2C时序
2. **I2C总线时序**：OLED的I2C通信需要特定的时序要求
3. **模块间干扰**：电机初始化的延时可能干扰了OLED的正常初始化

### 修复方案

#### 修复3：调整初始化顺序和时序
**文件：** `APP/Src/Task_App.c`
**函数：** `Task_Init()`
**修改内容：**
1. 调整初始化顺序：Serial → OLED → MPU6050 → Motor
2. 在OLED初始化后添加50ms稳定延时
3. 添加OLED初始化测试显示

```c
void Task_Init(void)
{
    Serial_Init(); //初始化串口
    OLED_Init(); //OLED初始化（优先初始化，确保I2C时序正确）
    Delay(50); //OLED初始化后稳定延时

    // OLED初始化测试显示
    OLED_Printf(0, 0, 16, "System Init...");
    OLED_Printf(0, 16, 16, "OLED: OK");

    MPU6050_Init(); //MPU6050初始化
    Motor_Start(); //开启电机（放在最后，避免延时影响其他模块）
    // ...
}
```

### 技术原理
1. **时序优化**：确保OLED的I2C初始化在任何可能干扰的延时之前完成
2. **稳定性保证**：50ms延时确保OLED完全稳定后再进行后续操作
3. **即时验证**：初始化后立即显示测试内容，验证OLED功能正常

### 预期效果
✅ OLED正常显示系统信息
✅ 电机功能保持正常
✅ 所有模块协调工作


