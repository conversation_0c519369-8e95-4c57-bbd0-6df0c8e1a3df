******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 14:27:57 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007895


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009d88  00016278  R  X
  SRAM                  20200000   00008000  00000706  000078fa  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009d88   00009d88    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008550   00008550    r-x .text
  00008610    00008610    00001700   00001700    r-- .rodata
  00009d10    00009d10    00000078   00000078    r-- .cinit
20200000    20200000    00000507   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000133   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008550     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001fe8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000021c4    000001b0     Task.o (.text.Task_Start)
                  00002374    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002514    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000026a6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000026a8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002830    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000029b8    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002b30    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002ca0    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002de4    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002f20    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003054    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003188    00000130     OLED.o (.text.OLED_ShowChar)
                  000032b8    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000033e8    00000128     Task_App.o (.text.Task_Tracker)
                  00003510    00000128     inv_mpu.o (.text.mpu_init)
                  00003638    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  0000375c    00000124     Task_App.o (.text.Task_Init)
                  00003880    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000039a4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003ac4    00000110     OLED.o (.text.OLED_Init)
                  00003bd4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003ce0    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003de8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003eec    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003fec    000000f0     Motor.o (.text.Motor_SetDirc)
                  000040dc    000000f0     Task_App.o (.text.Task_Motor_PID)
                  000041cc    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  000042b8    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  0000439c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004480    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004564    000000e0     Task_App.o (.text.Task_OLED)
                  00004644    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004720    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000047fc    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000048d4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000049ac    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004a80    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004b50    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004c14    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004cd8    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004d94    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004e4c    000000b4     Task.o (.text.Task_Add)
                  00004f00    000000ac     Task_App.o (.text.Task_Serial)
                  00004fac    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00005058    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005104    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000051ae    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000051b0    000000a2                            : udivmoddi4.S.obj (.text)
                  00005252    00000002     --HOLE-- [fill = 0]
                  00005254    000000a0     Motor.o (.text.Motor_SetDuty)
                  000052f4    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005394    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005430    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000054c8    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00005560    00000096     MPU6050.o (.text.inv_row_2_scale)
                  000055f6    00000002     --HOLE-- [fill = 0]
                  000055f8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00005684    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005710    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  0000579c    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005820    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000058a4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005926    00000002     --HOLE-- [fill = 0]
                  00005928    00000080     Motor.o (.text.Motor_GetSpeed)
                  000059a8    00000080     Motor.o (.text.Motor_Start)
                  00005a28    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005aa4    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005b18    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005b20    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005b94    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005c08    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005c7a    00000002     --HOLE-- [fill = 0]
                  00005c7c    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005cec    0000006e     OLED.o (.text.OLED_ShowString)
                  00005d5a    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00005dc6    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005e30    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005e98    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005efe    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005f64    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005fc8    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000602c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000608e    00000002     --HOLE-- [fill = 0]
                  00006090    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000060f2    00000002     --HOLE-- [fill = 0]
                  000060f4    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006154    00000060     Key_Led.o (.text.Key_Read)
                  000061b4    00000060     Task_App.o (.text.Task_IdleFunction)
                  00006214    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006274    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000062d4    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006334    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006392    00000002     --HOLE-- [fill = 0]
                  00006394    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000063f0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000644c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000064a8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006504    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000655c    00000058     Serial.o (.text.Serial_Init)
                  000065b4    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000660c    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006664    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000066ba    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000670c    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  0000675c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000067ac    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000067fc    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00006848    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006894    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000068e0    0000004c     OLED.o (.text.OLED_Printf)
                  0000692c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00006978    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000069c2    00000002     --HOLE-- [fill = 0]
                  000069c4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006a0e    00000002     --HOLE-- [fill = 0]
                  00006a10    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006a58    00000048     ADC.o (.text.adc_getValue)
                  00006aa0    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006ae8    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006b30    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006b78    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006bbc    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006c00    00000044     Task_App.o (.text.Task_Key)
                  00006c44    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006c88    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006ccc    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006d10    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00006d52    00000002     --HOLE-- [fill = 0]
                  00006d54    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006d96    00000002     --HOLE-- [fill = 0]
                  00006d98    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00006dd8    00000040     Interrupt.o (.text.Interrupt_Init)
                  00006e18    00000040     Task_App.o (.text.Task_GraySensor)
                  00006e58    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006e98    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006ed8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006f18    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006f58    0000003e     Task.o (.text.Task_CMP)
                  00006f96    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006fd4    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007010    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000704c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007088    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  000070c4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00007100    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  0000713c    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00007178    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000071b4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000071f0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000722c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00007266    00000002     --HOLE-- [fill = 0]
                  00007268    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000072a2    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  000072da    00000002     --HOLE-- [fill = 0]
                  000072dc    00000038     Task_App.o (.text.Task_LED)
                  00007314    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0000734c    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007380    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000073b4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000073e8    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  0000741c    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  0000744e    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00007480    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  000074b0    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  000074e0    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007510    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007540    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007570    00000030            : vsnprintf.c.obj (.text._outs)
                  000075a0    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000075d0    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007600    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000762c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007658    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007684    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  000076b0    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  000076da    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007702    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000772a    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007752    00000002     --HOLE-- [fill = 0]
                  00007754    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  0000777c    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000077a4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000077cc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000077f4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  0000781c    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007844    00000028     SysTick.o (.text.SysTick_Increasment)
                  0000786c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007894    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000078bc    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000078e2    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007908    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000792e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007954    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007978    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  0000799c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000079c0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000079e2    00000002     --HOLE-- [fill = 0]
                  000079e4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007a04    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007a24    00000020     SysTick.o (.text.Delay)
                  00007a44    00000020     main.o (.text.main)
                  00007a64    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007a84    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007aa2    00000002     --HOLE-- [fill = 0]
                  00007aa4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007ac2    00000002     --HOLE-- [fill = 0]
                  00007ac4    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007ae0    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007afc    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007b18    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007b34    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007b50    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007b6c    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007b88    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007ba4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007bc0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007bdc    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007bf8    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007c14    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007c30    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007c4c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007c68    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007c84    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007ca0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007cbc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007cd8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00007cf4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007d10    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00007d28    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00007d40    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007d58    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007d70    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007d88    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007da0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007db8    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007dd0    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007de8    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007e00    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007e18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007e30    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007e48    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007e60    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007e78    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00007e90    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007ea8    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007ec0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007ed8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007ef0    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007f08    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007f20    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007f38    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007f50    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007f68    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007f80    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007f98    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007fb0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007fc8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007fe0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007ff8    00000018     OLED.o (.text.DL_I2C_reset)
                  00008010    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00008028    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00008040    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00008058    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00008070    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008088    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000080a0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000080b8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000080d0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000080e8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00008100    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00008118    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00008130    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00008148    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00008160    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00008178    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00008190    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  000081a8    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000081c0    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000081d8    00000018            : vsprintf.c.obj (.text._outs)
                  000081f0    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00008206    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  0000821c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00008232    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00008248    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000825e    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00008274    00000016     OLED.o (.text.DL_GPIO_readPins)
                  0000828a    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  000082a0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000082b6    00000016     SysTick.o (.text.SysGetTick)
                  000082cc    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000082e2    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  000082f6    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  0000830a    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  0000831e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00008332    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00008346    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000835a    00000002     --HOLE-- [fill = 0]
                  0000835c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00008370    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00008384    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008398    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000083ac    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000083c0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000083d4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000083e8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000083fc    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00008410    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00008424    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00008438    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000844a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000845c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000846e    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  0000847e    00000002     --HOLE-- [fill = 0]
                  00008480    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00008490    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000084a0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000084b0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000084c0    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  000084ce    00000002     --HOLE-- [fill = 0]
                  000084d0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000084de    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000084ec    0000000e     MPU6050.o (.text.tap_cb)
                  000084fa    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00008508    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00008514    0000000c     SysTick.o (.text.Sys_GetTick)
                  00008520    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000852a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008534    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008544    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000854e    00000002     --HOLE-- [fill = 0]
                  00008550    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008560    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000856a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008574    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000857e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008588    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00008598    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  000085a2    0000000a     MPU6050.o (.text.android_orient_cb)
                  000085ac    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000085b4    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000085bc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000085c4    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000085ca    00000002     --HOLE-- [fill = 0]
                  000085cc    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000085dc    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000085e2    00000006            : exit.c.obj (.text:abort)
                  000085e8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000085ec    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000085f0    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000085f4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000085f8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00008608    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000860c    00000004     --HOLE-- [fill = 0]

.cinit     0    00009d10    00000078     
                  00009d10    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  00009d5e    00000002     --HOLE-- [fill = 0]
                  00009d60    0000000c     (__TI_handler_table)
                  00009d6c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009d74    00000010     (__TI_cinit_table)
                  00009d84    00000004     --HOLE-- [fill = 0]

.rodata    0    00008610    00001700     
                  00008610    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00009206    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  000097f6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00009a1e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009a20    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009b21    00000007     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009b28    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009b68    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009b90    00000028     inv_mpu.o (.rodata.test)
                  00009bb8    0000001f     Task_App.o (.rodata.str1.13861004553356644102.1)
                  00009bd7    0000001e     inv_mpu.o (.rodata.reg)
                  00009bf5    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00009bf8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009c10    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009c28    00000014     Task_App.o (.rodata.str1.3850258909703972507.1)
                  00009c3c    00000014     Task_App.o (.rodata.str1.4769078833470683459.1)
                  00009c50    00000014     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00009c64    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00009c75    00000011     Task_App.o (.rodata.str1.13166305789289702848.1)
                  00009c86    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00009c97    0000000f     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00009ca6    0000000c     inv_mpu.o (.rodata.hw)
                  00009cb2    0000000c     Task_App.o (.rodata.str1.7950429023856218820.1)
                  00009cbe    0000000b     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009cc9    00000001     --HOLE-- [fill = 0]
                  00009cca    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00009cd4    00000009     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00009cdd    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009cdf    00000001     --HOLE-- [fill = 0]
                  00009ce0    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00009ce8    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00009cf0    00000008     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009cf8    00000006     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009cfe    00000005     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009d03    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00009d07    00000004     Task_App.o (.rodata.str1.492715258893803702.1)
                  00009d0b    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009d0d    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000133     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f8    00000004     SysTick.o (.data.delayTick)
                  202004fc    00000004     SysTick.o (.data.uwTick)
                  20200500    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200502    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200503    00000001     Task_App.o (.data.Gray_Digtal)
                  20200504    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200505    00000001     Task.o (.data.Task_Num)
                  20200506    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1552    189       241    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2022    189       247    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2468    0         70     
       OLED_Font.o                      0       2072      0      
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          724     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8230    2072      975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     34080   6189      1798   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009d74 records: 2, size/record: 8, table size: 16
	.data: load addr=00009d10, load size=0000004e bytes, run addr=202003d4, run size=00000133 bytes, compression=lzss
	.bss: load addr=00009d6c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009d60 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002515     00008534     00008532   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000439d     00008550     0000854c   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008568          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000857c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000085b2          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000085e0          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003bd5     00008588     00008586   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000251f     000085cc     000085c8   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000085f2          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007895     000085f8     000085f4   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000085e9  ADC0_IRQHandler                      
000085e9  ADC1_IRQHandler                      
000085e9  AES_IRQHandler                       
000085ec  C$$EXIT                              
000085e9  CANFD0_IRQHandler                    
000085e9  DAC0_IRQHandler                      
00006d99  DL_ADC12_setClockConfig              
00008521  DL_Common_delayCycles                
00006849  DL_DMA_initChannel                   
00006335  DL_I2C_fillControllerTXFIFO          
00007089  DL_I2C_flushControllerTXFIFO         
0000792f  DL_I2C_setClockConfig                
00004645  DL_SYSCTL_configSYSPLL               
00005f65  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006b79  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003de9  DL_Timer_initFourCCPWMMode           
00007ca1  DL_Timer_setCaptCompUpdateMethod     
000080b9  DL_Timer_setCaptureCompareOutCtl     
00008491  DL_Timer_setCaptureCompareValue      
00007cbd  DL_Timer_setClockConfig              
00006a11  DL_UART_init                         
00008439  DL_UART_setClockConfig               
000085e9  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d7  Data_Tracker_Input                   
202004f0  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
000085e9  Default_Handler                      
00007a25  Delay                                
202003c8  ExISR_Flag                           
202004df  Flag_LED                             
20200502  Flag_MPU6050_Ready                   
000085e9  GROUP0_IRQHandler                    
000042b9  GROUP1_IRQHandler                    
00004721  Get_Analog_value                     
00007101  Get_Anolog_Value                     
000084c1  Get_Digtal_For_User                  
000072a3  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
20200503  Gray_Digtal                          
202004a0  Gray_Normal                          
000085ed  HOSTexit                             
000085e9  HardFault_Handler                    
000085e9  I2C0_IRQHandler                      
000085e9  I2C1_IRQHandler                      
00005dc7  I2C_OLED_Clear                       
0000713d  I2C_OLED_Set_Pos                     
00005431  I2C_OLED_WR_Byte                     
000060f5  I2C_OLED_i2c_sda_unlock              
00006dd9  Interrupt_Init                       
00006155  Key_Read                             
00002ca1  MPU6050_Init                         
202004e0  Motor                                
00005929  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
00005255  Motor_SetDuty                        
000059a9  Motor_Start                          
00005c7d  MyPrintf_DMA                         
000085e9  NMI_Handler                          
000026a9  No_MCU_Ganv_Sensor_Init              
00005c09  No_MCU_Ganv_Sensor_Init_Frist        
00006d11  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003ac5  OLED_Init                            
000068e1  OLED_Printf                          
00003189  OLED_ShowChar                        
00005ced  OLED_ShowString                      
000076b1  PID_IQ_Init                          
00003639  PID_IQ_Prosc                         
00006bbd  PID_IQ_SetParams                     
000085e9  PendSV_Handler                       
000085e9  RTC_IRQHandler                       
0000159d  Read_Quad                            
000085f5  Reset_Handler                        
000085e9  SPI0_IRQHandler                      
000085e9  SPI1_IRQHandler                      
000085e9  SVC_Handler                          
0000692d  SYSCFG_DL_ADC1_init                  
000074e1  SYSCFG_DL_DMA_CH_RX_init             
00008179  SYSCFG_DL_DMA_CH_TX_init             
00008509  SYSCFG_DL_DMA_init                   
00001e09  SYSCFG_DL_GPIO_init                  
00006505  SYSCFG_DL_I2C_MPU6050_init           
00005fc9  SYSCFG_DL_I2C_OLED_init              
000055f9  SYSCFG_DL_Motor_PWM_init             
00006395  SYSCFG_DL_SYSCTL_init                
000084a1  SYSCFG_DL_SYSTICK_init               
0000579d  SYSCFG_DL_UART0_init                 
00007601  SYSCFG_DL_init                       
000052f5  SYSCFG_DL_initPower                  
0000655d  Serial_Init                          
20200000  Serial_RxData                        
000082b7  SysGetTick                           
00005b19  SysTick_Handler                      
00007845  SysTick_Increasment                  
00008515  Sys_GetTick                          
000085e9  TIMA0_IRQHandler                     
000085e9  TIMA1_IRQHandler                     
000085e9  TIMG0_IRQHandler                     
000085e9  TIMG12_IRQHandler                    
000085e9  TIMG6_IRQHandler                     
000085e9  TIMG7_IRQHandler                     
000085e9  TIMG8_IRQHandler                     
0000844b  TI_memcpy_small                      
000084fb  TI_memset_small                      
00004e4d  Task_Add                             
00006e19  Task_GraySensor                      
000061b5  Task_IdleFunction                    
0000375d  Task_Init                            
00006c01  Task_Key                             
000072dd  Task_LED                             
000040dd  Task_Motor_PID                       
00004565  Task_OLED                            
00004f01  Task_Serial                          
000021c5  Task_Start                           
000033e9  Task_Tracker                         
000085e9  UART0_IRQHandler                     
000085e9  UART1_IRQHandler                     
000085e9  UART2_IRQHandler                     
000085e9  UART3_IRQHandler                     
00008191  _IQ24div                             
000081a9  _IQ24mpy                             
00007511  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009d74  __TI_CINIT_Base                      
00009d84  __TI_CINIT_Limit                     
00009d84  __TI_CINIT_Warm                      
00009d60  __TI_Handler_Table_Base              
00009d6c  __TI_Handler_Table_Limit             
000071f1  __TI_auto_init_nobinit_nopinit       
00005a29  __TI_decompress_lzss                 
0000845d  __TI_decompress_none                 
000065b5  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000082cd  __TI_zero_init_nomemset              
0000251f  __adddf3                             
000048df  __addsf3                             
00009a20  __aeabi_ctype_table_                 
00009a20  __aeabi_ctype_table_C                
00005b21  __aeabi_d2f                          
000069c5  __aeabi_d2iz                         
00006d55  __aeabi_d2uiz                        
0000251f  __aeabi_dadd                         
0000602d  __aeabi_dcmpeq                       
00006069  __aeabi_dcmpge                       
0000607d  __aeabi_dcmpgt                       
00006055  __aeabi_dcmple                       
00006041  __aeabi_dcmplt                       
00003bd5  __aeabi_ddiv                         
0000439d  __aeabi_dmul                         
00002515  __aeabi_dsub                         
202004f4  __aeabi_errno                        
000085b5  __aeabi_errno_addr                   
00006e99  __aeabi_f2d                          
00007315  __aeabi_f2iz                         
000048df  __aeabi_fadd                         
00006091  __aeabi_fcmpeq                       
000060cd  __aeabi_fcmpge                       
000060e1  __aeabi_fcmpgt                       
000060b9  __aeabi_fcmple                       
000060a5  __aeabi_fcmplt                       
000058a5  __aeabi_fdiv                         
00005685  __aeabi_fmul                         
000048d5  __aeabi_fsub                         
00007659  __aeabi_i2d                          
00007179  __aeabi_i2f                          
00006665  __aeabi_idiv                         
000026a7  __aeabi_idiv0                        
00006665  __aeabi_idivmod                      
000051af  __aeabi_ldiv0                        
00007aa5  __aeabi_llsl                         
0000799d  __aeabi_lmul                         
000085bd  __aeabi_memcpy                       
000085bd  __aeabi_memcpy4                      
000085bd  __aeabi_memcpy8                      
000084d1  __aeabi_memset                       
000084d1  __aeabi_memset4                      
000084d1  __aeabi_memset8                      
00007979  __aeabi_ui2d                         
0000786d  __aeabi_ui2f                         
00006e59  __aeabi_uidiv                        
00006e59  __aeabi_uidivmod                     
000083e9  __aeabi_uldivmod                     
00007aa5  __ashldi3                            
ffffffff  __binit__                            
00005e31  __cmpdf2                             
0000722d  __cmpsf2                             
00003bd5  __divdf3                             
000058a5  __divsf3                             
00005e31  __eqdf2                              
0000722d  __eqsf2                              
00006e99  __extendsfdf2                        
000069c5  __fixdfsi                            
00007315  __fixsfsi                            
00006d55  __fixunsdfsi                         
00007659  __floatsidf                          
00007179  __floatsisf                          
00007979  __floatunsidf                        
0000786d  __floatunsisf                        
00005aa5  __gedf2                              
000071b5  __gesf2                              
00005aa5  __gtdf2                              
000071b5  __gtsf2                              
00005e31  __ledf2                              
0000722d  __lesf2                              
00005e31  __ltdf2                              
0000722d  __ltsf2                              
UNDEFED   __mpu_init                           
0000439d  __muldf3                             
0000799d  __muldi3                             
00007269  __muldsi3                            
00005685  __mulsf3                             
00005e31  __nedf2                              
0000722d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002515  __subdf3                             
000048d5  __subsf3                             
00005b21  __truncdfsf2                         
000051b1  __udivmoddi4                         
00007895  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00008609  _system_pre_init                     
000085e3  abort                                
00006a59  adc_getValue                         
000097f6  asc2_0806                            
00009206  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002831  atan2                                
00002831  atan2l                               
00000df5  atanl                                
00006ed9  atoi                                 
ffffffff  binit                                
00005d5b  convertAnalogToDigital               
202004f8  delayTick                            
00006aa1  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00006215  dmp_enable_gyro_cal                  
00006ae9  dmp_enable_lp_quat                   
00007cf5  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
000083fd  dmp_register_android_orient_cb       
00008411  dmp_register_tap_cb                  
000054c9  dmp_set_fifo_rate                    
000029b9  dmp_set_orientation                  
00006c45  dmp_set_shake_reject_thresh          
0000741d  dmp_set_shake_reject_time            
0000744f  dmp_set_shake_reject_timeout         
00005eff  dmp_set_tap_axes                     
00006c89  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
000075a1  dmp_set_tap_time                     
000075d1  dmp_set_tap_time_multi               
20200506  enable_group1_irq                    
000063f1  frexp                                
000063f1  frexpl                               
00009ca6  hw                                   
00000000  interruptVectors                     
000047fd  ldexp                                
000047fd  ldexpl                               
00007a45  main                                 
000079c1  memccpy                              
00007a65  memcmp                               
202003d2  more                                 
00006275  mpu6050_i2c_sda_unlock               
00004cd9  mpu_configure_fifo                   
00005b95  mpu_get_accel_fsr                    
000062d5  mpu_get_gyro_fsr                     
000073e9  mpu_get_sample_rate                  
00003511  mpu_init                             
00003881  mpu_load_firmware                    
00003eed  mpu_lp_accel_mode                    
00003ce1  mpu_read_fifo_stream                 
00004fad  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
00004481  mpu_set_accel_fsr                    
00002375  mpu_set_bypass                       
00004d95  mpu_set_dmp_state                    
00004b51  mpu_set_gyro_fsr                     
00005395  mpu_set_int_latched                  
00004a81  mpu_set_lpf                          
000041cd  mpu_set_sample_rate                  
000032b9  mpu_set_sensors                      
00005059  mpu_write_mem                        
00002f21  mspm0_i2c_read                       
00004c15  mspm0_i2c_write                      
00005105  normalizeAnalogValues                
00003055  qsort                                
202003a0  quat                                 
00009bd7  reg                                  
000047fd  scalbn                               
000047fd  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002b31  sqrt                                 
00002b31  sqrtl                                
00009b90  test                                 
202004fc  uwTick                               
00006f19  vsnprintf                            
00007685  vsprintf                             
000084b1  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001e09  SYSCFG_DL_GPIO_init                  
000021c5  Task_Start                           
00002375  mpu_set_bypass                       
00002515  __aeabi_dsub                         
00002515  __subdf3                             
0000251f  __adddf3                             
0000251f  __aeabi_dadd                         
000026a7  __aeabi_idiv0                        
000026a9  No_MCU_Ganv_Sensor_Init              
00002831  atan2                                
00002831  atan2l                               
000029b9  dmp_set_orientation                  
00002b31  sqrt                                 
00002b31  sqrtl                                
00002ca1  MPU6050_Init                         
00002f21  mspm0_i2c_read                       
00003055  qsort                                
00003189  OLED_ShowChar                        
000032b9  mpu_set_sensors                      
000033e9  Task_Tracker                         
00003511  mpu_init                             
00003639  PID_IQ_Prosc                         
0000375d  Task_Init                            
00003881  mpu_load_firmware                    
00003ac5  OLED_Init                            
00003bd5  __aeabi_ddiv                         
00003bd5  __divdf3                             
00003ce1  mpu_read_fifo_stream                 
00003de9  DL_Timer_initFourCCPWMMode           
00003eed  mpu_lp_accel_mode                    
000040dd  Task_Motor_PID                       
000041cd  mpu_set_sample_rate                  
000042b9  GROUP1_IRQHandler                    
0000439d  __aeabi_dmul                         
0000439d  __muldf3                             
00004481  mpu_set_accel_fsr                    
00004565  Task_OLED                            
00004645  DL_SYSCTL_configSYSPLL               
00004721  Get_Analog_value                     
000047fd  ldexp                                
000047fd  ldexpl                               
000047fd  scalbn                               
000047fd  scalbnl                              
000048d5  __aeabi_fsub                         
000048d5  __subsf3                             
000048df  __addsf3                             
000048df  __aeabi_fadd                         
00004a81  mpu_set_lpf                          
00004b51  mpu_set_gyro_fsr                     
00004c15  mspm0_i2c_write                      
00004cd9  mpu_configure_fifo                   
00004d95  mpu_set_dmp_state                    
00004e4d  Task_Add                             
00004f01  Task_Serial                          
00004fad  mpu_read_mem                         
00005059  mpu_write_mem                        
00005105  normalizeAnalogValues                
000051af  __aeabi_ldiv0                        
000051b1  __udivmoddi4                         
00005255  Motor_SetDuty                        
000052f5  SYSCFG_DL_initPower                  
00005395  mpu_set_int_latched                  
00005431  I2C_OLED_WR_Byte                     
000054c9  dmp_set_fifo_rate                    
000055f9  SYSCFG_DL_Motor_PWM_init             
00005685  __aeabi_fmul                         
00005685  __mulsf3                             
0000579d  SYSCFG_DL_UART0_init                 
000058a5  __aeabi_fdiv                         
000058a5  __divsf3                             
00005929  Motor_GetSpeed                       
000059a9  Motor_Start                          
00005a29  __TI_decompress_lzss                 
00005aa5  __gedf2                              
00005aa5  __gtdf2                              
00005b19  SysTick_Handler                      
00005b21  __aeabi_d2f                          
00005b21  __truncdfsf2                         
00005b95  mpu_get_accel_fsr                    
00005c09  No_MCU_Ganv_Sensor_Init_Frist        
00005c7d  MyPrintf_DMA                         
00005ced  OLED_ShowString                      
00005d5b  convertAnalogToDigital               
00005dc7  I2C_OLED_Clear                       
00005e31  __cmpdf2                             
00005e31  __eqdf2                              
00005e31  __ledf2                              
00005e31  __ltdf2                              
00005e31  __nedf2                              
00005eff  dmp_set_tap_axes                     
00005f65  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005fc9  SYSCFG_DL_I2C_OLED_init              
0000602d  __aeabi_dcmpeq                       
00006041  __aeabi_dcmplt                       
00006055  __aeabi_dcmple                       
00006069  __aeabi_dcmpge                       
0000607d  __aeabi_dcmpgt                       
00006091  __aeabi_fcmpeq                       
000060a5  __aeabi_fcmplt                       
000060b9  __aeabi_fcmple                       
000060cd  __aeabi_fcmpge                       
000060e1  __aeabi_fcmpgt                       
000060f5  I2C_OLED_i2c_sda_unlock              
00006155  Key_Read                             
000061b5  Task_IdleFunction                    
00006215  dmp_enable_gyro_cal                  
00006275  mpu6050_i2c_sda_unlock               
000062d5  mpu_get_gyro_fsr                     
00006335  DL_I2C_fillControllerTXFIFO          
00006395  SYSCFG_DL_SYSCTL_init                
000063f1  frexp                                
000063f1  frexpl                               
00006505  SYSCFG_DL_I2C_MPU6050_init           
0000655d  Serial_Init                          
000065b5  __TI_ltoa                            
00006665  __aeabi_idiv                         
00006665  __aeabi_idivmod                      
00006849  DL_DMA_initChannel                   
000068e1  OLED_Printf                          
0000692d  SYSCFG_DL_ADC1_init                  
000069c5  __aeabi_d2iz                         
000069c5  __fixdfsi                            
00006a11  DL_UART_init                         
00006a59  adc_getValue                         
00006aa1  dmp_enable_6x_lp_quat                
00006ae9  dmp_enable_lp_quat                   
00006b79  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006bbd  PID_IQ_SetParams                     
00006c01  Task_Key                             
00006c45  dmp_set_shake_reject_thresh          
00006c89  dmp_set_tap_count                    
00006d11  No_Mcu_Ganv_Sensor_Task_Without_tick 
00006d55  __aeabi_d2uiz                        
00006d55  __fixunsdfsi                         
00006d99  DL_ADC12_setClockConfig              
00006dd9  Interrupt_Init                       
00006e19  Task_GraySensor                      
00006e59  __aeabi_uidiv                        
00006e59  __aeabi_uidivmod                     
00006e99  __aeabi_f2d                          
00006e99  __extendsfdf2                        
00006ed9  atoi                                 
00006f19  vsnprintf                            
00007089  DL_I2C_flushControllerTXFIFO         
00007101  Get_Anolog_Value                     
0000713d  I2C_OLED_Set_Pos                     
00007179  __aeabi_i2f                          
00007179  __floatsisf                          
000071b5  __gesf2                              
000071b5  __gtsf2                              
000071f1  __TI_auto_init_nobinit_nopinit       
0000722d  __cmpsf2                             
0000722d  __eqsf2                              
0000722d  __lesf2                              
0000722d  __ltsf2                              
0000722d  __nesf2                              
00007269  __muldsi3                            
000072a3  Get_Normalize_For_User               
000072dd  Task_LED                             
00007315  __aeabi_f2iz                         
00007315  __fixsfsi                            
000073e9  mpu_get_sample_rate                  
0000741d  dmp_set_shake_reject_time            
0000744f  dmp_set_shake_reject_timeout         
000074e1  SYSCFG_DL_DMA_CH_RX_init             
00007511  _IQ24toF                             
000075a1  dmp_set_tap_time                     
000075d1  dmp_set_tap_time_multi               
00007601  SYSCFG_DL_init                       
00007659  __aeabi_i2d                          
00007659  __floatsidf                          
00007685  vsprintf                             
000076b1  PID_IQ_Init                          
00007845  SysTick_Increasment                  
0000786d  __aeabi_ui2f                         
0000786d  __floatunsisf                        
00007895  _c_int00_noargs                      
0000792f  DL_I2C_setClockConfig                
00007979  __aeabi_ui2d                         
00007979  __floatunsidf                        
0000799d  __aeabi_lmul                         
0000799d  __muldi3                             
000079c1  memccpy                              
00007a25  Delay                                
00007a45  main                                 
00007a65  memcmp                               
00007aa5  __aeabi_llsl                         
00007aa5  __ashldi3                            
00007ca1  DL_Timer_setCaptCompUpdateMethod     
00007cbd  DL_Timer_setClockConfig              
00007cf5  dmp_load_motion_driver_firmware      
000080b9  DL_Timer_setCaptureCompareOutCtl     
00008179  SYSCFG_DL_DMA_CH_TX_init             
00008191  _IQ24div                             
000081a9  _IQ24mpy                             
000082b7  SysGetTick                           
000082cd  __TI_zero_init_nomemset              
000083e9  __aeabi_uldivmod                     
000083fd  dmp_register_android_orient_cb       
00008411  dmp_register_tap_cb                  
00008439  DL_UART_setClockConfig               
0000844b  TI_memcpy_small                      
0000845d  __TI_decompress_none                 
00008491  DL_Timer_setCaptureCompareValue      
000084a1  SYSCFG_DL_SYSTICK_init               
000084b1  wcslen                               
000084c1  Get_Digtal_For_User                  
000084d1  __aeabi_memset                       
000084d1  __aeabi_memset4                      
000084d1  __aeabi_memset8                      
000084fb  TI_memset_small                      
00008509  SYSCFG_DL_DMA_init                   
00008515  Sys_GetTick                          
00008521  DL_Common_delayCycles                
000085b5  __aeabi_errno_addr                   
000085bd  __aeabi_memcpy                       
000085bd  __aeabi_memcpy4                      
000085bd  __aeabi_memcpy8                      
000085e3  abort                                
000085e9  ADC0_IRQHandler                      
000085e9  ADC1_IRQHandler                      
000085e9  AES_IRQHandler                       
000085e9  CANFD0_IRQHandler                    
000085e9  DAC0_IRQHandler                      
000085e9  DMA_IRQHandler                       
000085e9  Default_Handler                      
000085e9  GROUP0_IRQHandler                    
000085e9  HardFault_Handler                    
000085e9  I2C0_IRQHandler                      
000085e9  I2C1_IRQHandler                      
000085e9  NMI_Handler                          
000085e9  PendSV_Handler                       
000085e9  RTC_IRQHandler                       
000085e9  SPI0_IRQHandler                      
000085e9  SPI1_IRQHandler                      
000085e9  SVC_Handler                          
000085e9  TIMA0_IRQHandler                     
000085e9  TIMA1_IRQHandler                     
000085e9  TIMG0_IRQHandler                     
000085e9  TIMG12_IRQHandler                    
000085e9  TIMG6_IRQHandler                     
000085e9  TIMG7_IRQHandler                     
000085e9  TIMG8_IRQHandler                     
000085e9  UART0_IRQHandler                     
000085e9  UART1_IRQHandler                     
000085e9  UART2_IRQHandler                     
000085e9  UART3_IRQHandler                     
000085ec  C$$EXIT                              
000085ed  HOSTexit                             
000085f5  Reset_Handler                        
00008609  _system_pre_init                     
00009206  asc2_1608                            
000097f6  asc2_0806                            
00009a20  __aeabi_ctype_table_                 
00009a20  __aeabi_ctype_table_C                
00009b90  test                                 
00009bd7  reg                                  
00009ca6  hw                                   
00009d60  __TI_Handler_Table_Base              
00009d6c  __TI_Handler_Table_Limit             
00009d74  __TI_CINIT_Base                      
00009d84  __TI_CINIT_Limit                     
00009d84  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004d7  Data_Tracker_Input                   
202004df  Flag_LED                             
202004e0  Motor                                
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202004f0  Data_Tracker_Offset                  
202004f4  __aeabi_errno                        
202004f8  delayTick                            
202004fc  uwTick                               
20200502  Flag_MPU6050_Ready                   
20200503  Gray_Digtal                          
20200506  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[326 symbols]
