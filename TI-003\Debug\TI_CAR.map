******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul 30 16:31:43 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00006c69


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008ba0  00017460  R  X
  SRAM                  20200000   00008000  00000705  000078fb  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008ba0   00008ba0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007410   00007410    r-x .text
  000074d0    000074d0    00001650   00001650    r-- .rodata
  00008b20    00008b20    00000080   00000080    r-- .cinit
20200000    20200000    00000506   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000132   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007410     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001fe4    000001b0     Task.o (.text.Task_Start)
                  00002194    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002334    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000024c6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000024c8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002650    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000027d8    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002950    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002ac0    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002c04    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002d40    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00002e74    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00002fa8    00000130     OLED.o (.text.OLED_ShowChar)
                  000030d8    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003208    00000128     inv_mpu.o (.text.mpu_init)
                  00003330    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003454    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003578    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003698    00000110     OLED.o (.text.OLED_Init)
                  000037a8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000038b4    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  000039bc    00000100     Tracker.o (.text.GrayscaleTracker_Read)
                  00003abc    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003bbc    000000f0     Motor.o (.text.Motor_SetDirc)
                  00003cac    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00003d9c    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003e88    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00003f6c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004050    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004134    000000e0     Task_App.o (.text.Task_OLED)
                  00004214    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000042f0    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000043c8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000044a0    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004574    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004644    000000c4     Task_App.o (.text.Task_Init)
                  00004708    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000047cc    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004890    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  0000494c    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004a04    000000b4     Task.o (.text.Task_Add)
                  00004ab8    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004b64    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004c10    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00004cba    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004cbc    000000a2                            : udivmoddi4.S.obj (.text)
                  00004d5e    00000002     --HOLE-- [fill = 0]
                  00004d60    000000a0     Motor.o (.text.Motor_SetDuty)
                  00004e00    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004e9c    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00004f34    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004fcc    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005062    00000002     --HOLE-- [fill = 0]
                  00005064    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000050f0    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  0000517c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000051fe    00000002     --HOLE-- [fill = 0]
                  00005200    00000080     Motor.o (.text.Motor_GetSpeed)
                  00005280    00000080     Task_App.o (.text.Task_Serial)
                  00005300    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000537c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000053f0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005464    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000054d8    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  0000554a    00000002     --HOLE-- [fill = 0]
                  0000554c    00000070     Motor.o (.text.Motor_Start)
                  000055bc    00000070     Serial.o (.text.MyPrintf_DMA)
                  0000562c    0000006e     OLED.o (.text.OLED_ShowString)
                  0000569a    00000002     --HOLE-- [fill = 0]
                  0000569c    0000006c     Task_App.o (.text.Task_Tracker)
                  00005708    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00005774    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  000057de    00000002     --HOLE-- [fill = 0]
                  000057e0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005848    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000058ae    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005914    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005978    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000059da    00000002     --HOLE-- [fill = 0]
                  000059dc    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005a3e    00000002     --HOLE-- [fill = 0]
                  00005a40    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005aa0    00000060     Key_Led.o (.text.Key_Read)
                  00005b00    00000060     Task_App.o (.text.Task_IdleFunction)
                  00005b60    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005bc0    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005c20    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005c80    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005cde    00000002     --HOLE-- [fill = 0]
                  00005ce0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00005d3c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00005d98    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00005df4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00005e4c    00000058     Serial.o (.text.Serial_Init)
                  00005ea4    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00005efc    00000058            : _printfi.c.obj (.text._pconv_f)
                  00005f54    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00005faa    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00005ffc    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  0000604c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  0000609c    0000004c     OLED.o (.text.OLED_Printf)
                  000060e8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006132    00000002     --HOLE-- [fill = 0]
                  00006134    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  0000617c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  000061c4    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  0000620c    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006250    00000044     Task_App.o (.text.Task_Key)
                  00006294    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  000062d8    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  0000631c    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006360    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000063a2    00000002     --HOLE-- [fill = 0]
                  000063a4    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000063e6    00000002     --HOLE-- [fill = 0]
                  000063e8    00000040     Interrupt.o (.text.Interrupt_Init)
                  00006428    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006468    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000064a8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000064e8    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006528    0000003e     Task.o (.text.Task_CMP)
                  00006566    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  000065a4    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000065e0    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000661c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006658    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006694    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000066d0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000670c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006748    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006782    00000002     --HOLE-- [fill = 0]
                  00006784    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000067be    00000002     --HOLE-- [fill = 0]
                  000067c0    00000038     Task_App.o (.text.Task_LED)
                  000067f8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006830    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006864    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006898    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  000068cc    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  000068fe    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006930    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006960    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00006990    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000069c0    00000030            : vsnprintf.c.obj (.text._outs)
                  000069f0    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00006a20    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006a50    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006a7c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006aa8    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00006ad4    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00006afe    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00006b26    00000028     OLED.o (.text.DL_Common_updateReg)
                  00006b4e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00006b76    00000002     --HOLE-- [fill = 0]
                  00006b78    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00006ba0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00006bc8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00006bf0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00006c18    00000028     SysTick.o (.text.SysTick_Increasment)
                  00006c40    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00006c68    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006c90    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00006cb6    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00006cdc    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00006d02    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00006d28    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00006d4c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00006d70    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00006d92    00000002     --HOLE-- [fill = 0]
                  00006d94    00000020     SysTick.o (.text.Delay)
                  00006db4    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00006dd4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00006df2    00000002     --HOLE-- [fill = 0]
                  00006df4    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00006e10    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00006e2c    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00006e48    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00006e64    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00006e80    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00006e9c    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00006eb8    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00006ed4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00006ef0    0000001c     main.o (.text.main)
                  00006f0c    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00006f24    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00006f3c    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00006f54    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00006f6c    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00006f84    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00006f9c    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00006fb4    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00006fcc    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00006fe4    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00006ffc    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007014    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  0000702c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007044    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000705c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007074    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  0000708c    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000070a4    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000070bc    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000070d4    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000070ec    00000018     OLED.o (.text.DL_I2C_reset)
                  00007104    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  0000711c    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007134    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  0000714c    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00007164    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  0000717c    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007194    00000018            : vsprintf.c.obj (.text._outs)
                  000071ac    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000071c2    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000071d8    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000071ee    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007204    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  0000721a    00000016     SysTick.o (.text.SysGetTick)
                  00007230    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00007246    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  0000725a    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  0000726e    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00007282    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007296    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000072aa    00000002     --HOLE-- [fill = 0]
                  000072ac    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000072c0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000072d4    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000072e8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000072fc    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007310    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007324    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007338    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000734c    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000735e    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007370    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007380    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007390    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000739e    00000002     --HOLE-- [fill = 0]
                  000073a0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000073ae    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000073bc    0000000e     MPU6050.o (.text.tap_cb)
                  000073ca    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  000073d8    0000000c     SysTick.o (.text.Sys_GetTick)
                  000073e4    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000073ee    00000002     --HOLE-- [fill = 0]
                  000073f0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007400    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000740a    00000002     --HOLE-- [fill = 0]
                  0000740c    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  0000741c    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007426    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007430    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000743a    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007444    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007454    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  0000745e    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007468    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007470    00000008     Interrupt.o (.text.SysTick_Handler)
                  00007478    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00007480    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007488    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000748e    00000002     --HOLE-- [fill = 0]
                  00007490    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000074a0    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000074a6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000074aa    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000074ae    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000074b2    00000002     --HOLE-- [fill = 0]
                  000074b4    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000074c4    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000074c8    00000004            : exit.c.obj (.text:abort)
                  000074cc    00000004     --HOLE-- [fill = 0]

.cinit     0    00008b20    00000080     
                  00008b20    00000059     (.cinit..data.load) [load image, compression = lzss]
                  00008b79    00000003     --HOLE-- [fill = 0]
                  00008b7c    0000000c     (__TI_handler_table)
                  00008b88    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008b90    00000010     (__TI_cinit_table)

.rodata    0    000074d0    00001650     
                  000074d0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000080c6    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  000086b6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  000088de    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000088e0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000089e1    00000007     Task_App.o (.rodata.str1.136405643080007560121)
                  000089e8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008a28    00000028     inv_mpu.o (.rodata.test)
                  00008a50    0000001e     inv_mpu.o (.rodata.reg)
                  00008a6e    00000014     Task_App.o (.rodata.str1.157702741485139367601)
                  00008a82    00000014     Task_App.o (.rodata.str1.182657883079055368591)
                  00008a96    00000014     Task_App.o (.rodata.str1.97872905622636903301)
                  00008aaa    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00008abb    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00008acc    00000011     Task_App.o (.rodata.str1.25142174965186748781)
                  00008add    00000001     --HOLE-- [fill = 0]
                  00008ade    0000000c     inv_mpu.o (.rodata.hw)
                  00008aea    0000000c     Task_App.o (.rodata.str1.183384535776591351011)
                  00008af6    00000008     Task_App.o (.rodata.str1.67400646179352630301)
                  00008afe    00000006     Task_App.o (.rodata.str1.115332825834609149281)
                  00008b04    00000005     Task_App.o (.rodata.str1.87978995337490384161)
                  00008b09    00000004     Task_App.o (.rodata.str1.134609064190095881641)
                  00008b0d    00000004     Task_App.o (.rodata.str1.171900814140190138471)
                  00008b11    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008b13    0000000d     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:sensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000132     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.black)
                  202004a0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004b0    00000010     Task_App.o (.data.white)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f8    00000004     SysTick.o (.data.delayTick)
                  202004fc    00000004     SysTick.o (.data.uwTick)
                  20200500    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200502    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200503    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200504    00000001     Task.o (.data.Task_Num)
                  20200505    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               490     4         0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           28      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           526     196       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1160    123       240    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1630    123       246    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2468    0         70     
       OLED_Font.o                      0       2072      0      
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1128    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          708     0         144    
       PID_IQMath.o                     402     0         0      
       Tracker.o                        256     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8118    2072      975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                         192     0         0      
       dl_timer.o                       16      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           208     0         0      
                                                                 
    D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       4       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8354    355       4      
                                                                 
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       125       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     29662   6015      1797   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008b90 records: 2, size/record: 8, table size: 16
	.data: load addr=00008b20, load size=00000059 bytes, run addr=202003d4, run size=00000132 bytes, compression=lzss
	.bss: load addr=00008b88, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008b7c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002335     000073f0     000073ec   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003f6d     0000740c     00007408   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007424          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007438          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000746e          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000074a4          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   000037a9     00007444     00007442   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000233f     00007490     0000748c   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000074ac          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00006c69     000074b4     000074ae   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                
-------   ----                                
000074a7  ADC0_IRQHandler                     
000074a7  ADC1_IRQHandler                     
000074a7  AES_IRQHandler                      
000074c8  C$$EXIT                             
000074a7  CANFD0_IRQHandler                   
000074a7  DAC0_IRQHandler                     
00005c81  DL_I2C_fillControllerTXFIFO         
0000661d  DL_I2C_flushControllerTXFIFO        
00006d03  DL_I2C_setClockConfig               
00007371  DL_Timer_setCaptureCompareValue     
000074a7  DMA_IRQHandler                      
202003b0  Data_Accel                          
202003b6  Data_Gyro                           
202004e8  Data_MotorEncoder                   
202004ec  Data_Motor_TarSpeed                 
202003bc  Data_Pitch                          
202003c0  Data_Roll                           
202004d7  Data_Tracker_Input                  
202004f0  Data_Tracker_Offset                 
202003c4  Data_Yaw                            
000074a7  Default_Handler                     
00006d95  Delay                               
202003c8  ExISR_Flag                          
202004df  Flag_LED                            
20200502  Flag_MPU6050_Ready                  
000074a7  GROUP0_IRQHandler                   
00003e89  GROUP1_IRQHandler                   
00004215  Get_Analog_value                    
00007391  Get_Digtal_For_User                 
000039bd  GrayscaleTracker_Read               
000074a7  HardFault_Handler                   
000074a7  I2C0_IRQHandler                     
000074a7  I2C1_IRQHandler                     
00005775  I2C_OLED_Clear                      
00006659  I2C_OLED_Set_Pos                    
00004e9d  I2C_OLED_WR_Byte                    
00005a41  I2C_OLED_i2c_sda_unlock             
000063e9  Interrupt_Init                      
00005aa1  Key_Read                            
00002ac1  MPU6050_Init                        
202004e0  Motor                               
00005201  Motor_GetSpeed                      
202003d4  Motor_Left                          
2020041c  Motor_Right                         
00004d61  Motor_SetDuty                       
0000554d  Motor_Start                         
000055bd  MyPrintf_DMA                        
000074a7  NMI_Handler                         
000024c9  No_MCU_Ganv_Sensor_Init             
000054d9  No_MCU_Ganv_Sensor_Init_Frist       
00006361  No_Mcu_Ganv_Sensor_Task_Without_tick
00003699  OLED_Init                           
0000609d  OLED_Printf                         
00002fa9  OLED_ShowChar                       
0000562d  OLED_ShowString                     
00006ad5  PID_IQ_Init                         
00003331  PID_IQ_Prosc                        
0000620d  PID_IQ_SetParams                    
000074a7  PendSV_Handler                      
000074a7  RTC_IRQHandler                      
0000159d  Read_Quad                           
000074af  Reset_Handler                       
000074a7  SPI0_IRQHandler                     
000074a7  SPI1_IRQHandler                     
000074a7  SVC_Handler                         
00005df5  SYSCFG_DL_I2C_MPU6050_init          
00005915  SYSCFG_DL_I2C_OLED_init             
00005e4d  Serial_Init                         
20200000  Serial_RxData                       
0000721b  SysGetTick                          
00007471  SysTick_Handler                     
00006c19  SysTick_Increasment                 
000073d9  Sys_GetTick                         
000074a7  TIMA0_IRQHandler                    
000074a7  TIMA1_IRQHandler                    
000074a7  TIMG0_IRQHandler                    
000074a7  TIMG12_IRQHandler                   
000074a7  TIMG6_IRQHandler                    
000074a7  TIMG7_IRQHandler                    
000074a7  TIMG8_IRQHandler                    
0000734d  TI_memcpy_small                     
000073cb  TI_memset_small                     
00004a05  Task_Add                            
00005b01  Task_IdleFunction                   
00004645  Task_Init                           
00006251  Task_Key                            
000067c1  Task_LED                            
00003cad  Task_Motor_PID                      
00004135  Task_OLED                           
00005281  Task_Serial                         
00001fe5  Task_Start                          
0000569d  Task_Tracker                        
000074a7  UART0_IRQHandler                    
000074a7  UART1_IRQHandler                    
000074a7  UART2_IRQHandler                    
000074a7  UART3_IRQHandler                    
0000714d  _IQ24div                            
00007165  _IQ24mpy                            
00006961  _IQ24toF                            
20208000  __STACK_END                         
00000200  __STACK_SIZE                        
00000000  __TI_ATRegion0_region_sz            
00000000  __TI_ATRegion0_src_addr             
00000000  __TI_ATRegion0_trg_addr             
00000000  __TI_ATRegion1_region_sz            
00000000  __TI_ATRegion1_src_addr             
00000000  __TI_ATRegion1_trg_addr             
00000000  __TI_ATRegion2_region_sz            
00000000  __TI_ATRegion2_src_addr             
00000000  __TI_ATRegion2_trg_addr             
00008b90  __TI_CINIT_Base                     
00008ba0  __TI_CINIT_Limit                    
00008ba0  __TI_CINIT_Warm                     
00008b7c  __TI_Handler_Table_Base             
00008b88  __TI_Handler_Table_Limit            
0000670d  __TI_auto_init_nobinit_nopinit      
00005301  __TI_decompress_lzss                
0000735f  __TI_decompress_none                
00005ea5  __TI_ltoa                           
ffffffff  __TI_pprof_out_hndl                 
000000c1  __TI_printfi                        
ffffffff  __TI_prof_data_size                 
ffffffff  __TI_prof_data_start                
00000000  __TI_static_base__                  
00007231  __TI_zero_init_nomemset             
0000233f  __adddf3                            
000043d3  __addsf3                            
000088e0  __aeabi_ctype_table_                
000088e0  __aeabi_ctype_table_C               
000053f1  __aeabi_d2f                         
000060e9  __aeabi_d2iz                        
000063a5  __aeabi_d2uiz                       
0000233f  __aeabi_dadd                        
00005979  __aeabi_dcmpeq                      
000059b5  __aeabi_dcmpge                      
000059c9  __aeabi_dcmpgt                      
000059a1  __aeabi_dcmple                      
0000598d  __aeabi_dcmplt                      
000037a9  __aeabi_ddiv                        
00003f6d  __aeabi_dmul                        
00002335  __aeabi_dsub                        
202004f4  __aeabi_errno                       
00007479  __aeabi_errno_addr                  
00006469  __aeabi_f2d                         
000067f9  __aeabi_f2iz                        
000043d3  __aeabi_fadd                        
000059dd  __aeabi_fcmpeq                      
00005a19  __aeabi_fcmpge                      
00005a2d  __aeabi_fcmpgt                      
00005a05  __aeabi_fcmple                      
000059f1  __aeabi_fcmplt                      
0000517d  __aeabi_fdiv                        
00005065  __aeabi_fmul                        
000043c9  __aeabi_fsub                        
00006a7d  __aeabi_i2d                         
00006695  __aeabi_i2f                         
00005f55  __aeabi_idiv                        
000024c7  __aeabi_idiv0                       
00005f55  __aeabi_idivmod                     
00004cbb  __aeabi_ldiv0                       
00006dd5  __aeabi_llsl                        
00006d4d  __aeabi_lmul                        
00007481  __aeabi_memcpy                      
00007481  __aeabi_memcpy4                     
00007481  __aeabi_memcpy8                     
000073a1  __aeabi_memset                      
000073a1  __aeabi_memset4                     
000073a1  __aeabi_memset8                     
00006d29  __aeabi_ui2d                        
00006c41  __aeabi_ui2f                        
00006429  __aeabi_uidiv                       
00006429  __aeabi_uidivmod                    
000072fd  __aeabi_uldivmod                    
00006dd5  __ashldi3                           
ffffffff  __binit__                           
000057e1  __cmpdf2                            
00006749  __cmpsf2                            
000037a9  __divdf3                            
0000517d  __divsf3                            
000057e1  __eqdf2                             
00006749  __eqsf2                             
00006469  __extendsfdf2                       
000060e9  __fixdfsi                           
000067f9  __fixsfsi                           
000063a5  __fixunsdfsi                        
00006a7d  __floatsidf                         
00006695  __floatsisf                         
00006d29  __floatunsidf                       
00006c41  __floatunsisf                       
0000537d  __gedf2                             
000066d1  __gesf2                             
0000537d  __gtdf2                             
000066d1  __gtsf2                             
000057e1  __ledf2                             
00006749  __lesf2                             
000057e1  __ltdf2                             
00006749  __ltsf2                             
UNDEFED   __mpu_init                          
00003f6d  __muldf3                            
00006d4d  __muldi3                            
00006785  __muldsi3                           
00005065  __mulsf3                            
000057e1  __nedf2                             
00006749  __nesf2                             
20207e00  __stack                             
20200000  __start___llvm_prf_bits             
20200000  __start___llvm_prf_cnts             
20200000  __stop___llvm_prf_bits              
20200000  __stop___llvm_prf_cnts              
00002335  __subdf3                            
000043c9  __subsf3                            
000053f1  __truncdfsf2                        
00004cbd  __udivmoddi4                        
00006c69  _c_int00_noargs                     
UNDEFED   _system_post_cinit                  
000074c5  _system_pre_init                    
000074c9  abort                               
UNDEFED   adc_getValue                        
000086b6  asc2_0806                           
000080c6  asc2_1608                           
00000a91  asin                                
00000a91  asinl                               
00000df5  atan                                
00002651  atan2                               
00002651  atan2l                              
00000df5  atanl                               
000064a9  atoi                                
ffffffff  binit                               
20200490  black                               
00005709  convertAnalogToDigital              
202004f8  delayTick                           
00006135  dmp_enable_6x_lp_quat               
000010ed  dmp_enable_feature                  
00005b61  dmp_enable_gyro_cal                 
0000617d  dmp_enable_lp_quat                  
00006ed5  dmp_load_motion_driver_firmware     
00001c15  dmp_read_fifo                       
00007311  dmp_register_android_orient_cb      
00007325  dmp_register_tap_cb                 
00004f35  dmp_set_fifo_rate                   
000027d9  dmp_set_orientation                 
00006295  dmp_set_shake_reject_thresh         
000068cd  dmp_set_shake_reject_time           
000068ff  dmp_set_shake_reject_timeout        
000058af  dmp_set_tap_axes                    
000062d9  dmp_set_tap_count                   
00001365  dmp_set_tap_thresh                  
000069f1  dmp_set_tap_time                    
00006a21  dmp_set_tap_time_multi              
20200505  enable_group1_irq                   
00005ce1  frexp                               
00005ce1  frexpl                              
00008ade  hw                                  
00000000  interruptVectors                    
000042f1  ldexp                               
000042f1  ldexpl                              
00006ef1  main                                
00006d71  memccpy                             
00006db5  memcmp                              
202003d2  more                                
00005bc1  mpu6050_i2c_sda_unlock              
00004891  mpu_configure_fifo                  
00005465  mpu_get_accel_fsr                   
00005c21  mpu_get_gyro_fsr                    
00006899  mpu_get_sample_rate                 
00003209  mpu_init                            
00003455  mpu_load_firmware                   
00003abd  mpu_lp_accel_mode                   
000038b5  mpu_read_fifo_stream                
00004ab9  mpu_read_mem                        
000017c9  mpu_reset_fifo                      
00004051  mpu_set_accel_fsr                   
00002195  mpu_set_bypass                      
0000494d  mpu_set_dmp_state                   
00004709  mpu_set_gyro_fsr                    
00004e01  mpu_set_int_latched                 
00004575  mpu_set_lpf                         
00003d9d  mpu_set_sample_rate                 
000030d9  mpu_set_sensors                     
00004b65  mpu_write_mem                       
00002d41  mspm0_i2c_read                      
000047cd  mspm0_i2c_write                     
00004c11  normalizeAnalogValues               
00002e75  qsort                               
202003a0  quat                                
00008a50  reg                                 
000042f1  scalbn                              
000042f1  scalbnl                             
202002f0  sensor                              
202003cc  sensor_timestamp                    
202003d0  sensors                             
00002951  sqrt                                
00002951  sqrtl                               
00008a28  test                                
202004fc  uwTick                              
000064e9  vsnprintf                           
00006aa9  vsprintf                            
00007381  wcslen                              
202004b0  white                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                
-------   ----                                
00000000  __TI_ATRegion0_region_sz            
00000000  __TI_ATRegion0_src_addr             
00000000  __TI_ATRegion0_trg_addr             
00000000  __TI_ATRegion1_region_sz            
00000000  __TI_ATRegion1_src_addr             
00000000  __TI_ATRegion1_trg_addr             
00000000  __TI_ATRegion2_region_sz            
00000000  __TI_ATRegion2_src_addr             
00000000  __TI_ATRegion2_trg_addr             
00000000  __TI_static_base__                  
00000000  interruptVectors                    
000000c1  __TI_printfi                        
00000200  __STACK_SIZE                        
00000a91  asin                                
00000a91  asinl                               
00000df5  atan                                
00000df5  atanl                               
000010ed  dmp_enable_feature                  
00001365  dmp_set_tap_thresh                  
0000159d  Read_Quad                           
000017c9  mpu_reset_fifo                      
00001c15  dmp_read_fifo                       
00001fe5  Task_Start                          
00002195  mpu_set_bypass                      
00002335  __aeabi_dsub                        
00002335  __subdf3                            
0000233f  __adddf3                            
0000233f  __aeabi_dadd                        
000024c7  __aeabi_idiv0                       
000024c9  No_MCU_Ganv_Sensor_Init             
00002651  atan2                               
00002651  atan2l                              
000027d9  dmp_set_orientation                 
00002951  sqrt                                
00002951  sqrtl                               
00002ac1  MPU6050_Init                        
00002d41  mspm0_i2c_read                      
00002e75  qsort                               
00002fa9  OLED_ShowChar                       
000030d9  mpu_set_sensors                     
00003209  mpu_init                            
00003331  PID_IQ_Prosc                        
00003455  mpu_load_firmware                   
00003699  OLED_Init                           
000037a9  __aeabi_ddiv                        
000037a9  __divdf3                            
000038b5  mpu_read_fifo_stream                
000039bd  GrayscaleTracker_Read               
00003abd  mpu_lp_accel_mode                   
00003cad  Task_Motor_PID                      
00003d9d  mpu_set_sample_rate                 
00003e89  GROUP1_IRQHandler                   
00003f6d  __aeabi_dmul                        
00003f6d  __muldf3                            
00004051  mpu_set_accel_fsr                   
00004135  Task_OLED                           
00004215  Get_Analog_value                    
000042f1  ldexp                               
000042f1  ldexpl                              
000042f1  scalbn                              
000042f1  scalbnl                             
000043c9  __aeabi_fsub                        
000043c9  __subsf3                            
000043d3  __addsf3                            
000043d3  __aeabi_fadd                        
00004575  mpu_set_lpf                         
00004645  Task_Init                           
00004709  mpu_set_gyro_fsr                    
000047cd  mspm0_i2c_write                     
00004891  mpu_configure_fifo                  
0000494d  mpu_set_dmp_state                   
00004a05  Task_Add                            
00004ab9  mpu_read_mem                        
00004b65  mpu_write_mem                       
00004c11  normalizeAnalogValues               
00004cbb  __aeabi_ldiv0                       
00004cbd  __udivmoddi4                        
00004d61  Motor_SetDuty                       
00004e01  mpu_set_int_latched                 
00004e9d  I2C_OLED_WR_Byte                    
00004f35  dmp_set_fifo_rate                   
00005065  __aeabi_fmul                        
00005065  __mulsf3                            
0000517d  __aeabi_fdiv                        
0000517d  __divsf3                            
00005201  Motor_GetSpeed                      
00005281  Task_Serial                         
00005301  __TI_decompress_lzss                
0000537d  __gedf2                             
0000537d  __gtdf2                             
000053f1  __aeabi_d2f                         
000053f1  __truncdfsf2                        
00005465  mpu_get_accel_fsr                   
000054d9  No_MCU_Ganv_Sensor_Init_Frist       
0000554d  Motor_Start                         
000055bd  MyPrintf_DMA                        
0000562d  OLED_ShowString                     
0000569d  Task_Tracker                        
00005709  convertAnalogToDigital              
00005775  I2C_OLED_Clear                      
000057e1  __cmpdf2                            
000057e1  __eqdf2                             
000057e1  __ledf2                             
000057e1  __ltdf2                             
000057e1  __nedf2                             
000058af  dmp_set_tap_axes                    
00005915  SYSCFG_DL_I2C_OLED_init             
00005979  __aeabi_dcmpeq                      
0000598d  __aeabi_dcmplt                      
000059a1  __aeabi_dcmple                      
000059b5  __aeabi_dcmpge                      
000059c9  __aeabi_dcmpgt                      
000059dd  __aeabi_fcmpeq                      
000059f1  __aeabi_fcmplt                      
00005a05  __aeabi_fcmple                      
00005a19  __aeabi_fcmpge                      
00005a2d  __aeabi_fcmpgt                      
00005a41  I2C_OLED_i2c_sda_unlock             
00005aa1  Key_Read                            
00005b01  Task_IdleFunction                   
00005b61  dmp_enable_gyro_cal                 
00005bc1  mpu6050_i2c_sda_unlock              
00005c21  mpu_get_gyro_fsr                    
00005c81  DL_I2C_fillControllerTXFIFO         
00005ce1  frexp                               
00005ce1  frexpl                              
00005df5  SYSCFG_DL_I2C_MPU6050_init          
00005e4d  Serial_Init                         
00005ea5  __TI_ltoa                           
00005f55  __aeabi_idiv                        
00005f55  __aeabi_idivmod                     
0000609d  OLED_Printf                         
000060e9  __aeabi_d2iz                        
000060e9  __fixdfsi                           
00006135  dmp_enable_6x_lp_quat               
0000617d  dmp_enable_lp_quat                  
0000620d  PID_IQ_SetParams                    
00006251  Task_Key                            
00006295  dmp_set_shake_reject_thresh         
000062d9  dmp_set_tap_count                   
00006361  No_Mcu_Ganv_Sensor_Task_Without_tick
000063a5  __aeabi_d2uiz                       
000063a5  __fixunsdfsi                        
000063e9  Interrupt_Init                      
00006429  __aeabi_uidiv                       
00006429  __aeabi_uidivmod                    
00006469  __aeabi_f2d                         
00006469  __extendsfdf2                       
000064a9  atoi                                
000064e9  vsnprintf                           
0000661d  DL_I2C_flushControllerTXFIFO        
00006659  I2C_OLED_Set_Pos                    
00006695  __aeabi_i2f                         
00006695  __floatsisf                         
000066d1  __gesf2                             
000066d1  __gtsf2                             
0000670d  __TI_auto_init_nobinit_nopinit      
00006749  __cmpsf2                            
00006749  __eqsf2                             
00006749  __lesf2                             
00006749  __ltsf2                             
00006749  __nesf2                             
00006785  __muldsi3                           
000067c1  Task_LED                            
000067f9  __aeabi_f2iz                        
000067f9  __fixsfsi                           
00006899  mpu_get_sample_rate                 
000068cd  dmp_set_shake_reject_time           
000068ff  dmp_set_shake_reject_timeout        
00006961  _IQ24toF                            
000069f1  dmp_set_tap_time                    
00006a21  dmp_set_tap_time_multi              
00006a7d  __aeabi_i2d                         
00006a7d  __floatsidf                         
00006aa9  vsprintf                            
00006ad5  PID_IQ_Init                         
00006c19  SysTick_Increasment                 
00006c41  __aeabi_ui2f                        
00006c41  __floatunsisf                       
00006c69  _c_int00_noargs                     
00006d03  DL_I2C_setClockConfig               
00006d29  __aeabi_ui2d                        
00006d29  __floatunsidf                       
00006d4d  __aeabi_lmul                        
00006d4d  __muldi3                            
00006d71  memccpy                             
00006d95  Delay                               
00006db5  memcmp                              
00006dd5  __aeabi_llsl                        
00006dd5  __ashldi3                           
00006ed5  dmp_load_motion_driver_firmware     
00006ef1  main                                
0000714d  _IQ24div                            
00007165  _IQ24mpy                            
0000721b  SysGetTick                          
00007231  __TI_zero_init_nomemset             
000072fd  __aeabi_uldivmod                    
00007311  dmp_register_android_orient_cb      
00007325  dmp_register_tap_cb                 
0000734d  TI_memcpy_small                     
0000735f  __TI_decompress_none                
00007371  DL_Timer_setCaptureCompareValue     
00007381  wcslen                              
00007391  Get_Digtal_For_User                 
000073a1  __aeabi_memset                      
000073a1  __aeabi_memset4                     
000073a1  __aeabi_memset8                     
000073cb  TI_memset_small                     
000073d9  Sys_GetTick                         
00007471  SysTick_Handler                     
00007479  __aeabi_errno_addr                  
00007481  __aeabi_memcpy                      
00007481  __aeabi_memcpy4                     
00007481  __aeabi_memcpy8                     
000074a7  ADC0_IRQHandler                     
000074a7  ADC1_IRQHandler                     
000074a7  AES_IRQHandler                      
000074a7  CANFD0_IRQHandler                   
000074a7  DAC0_IRQHandler                     
000074a7  DMA_IRQHandler                      
000074a7  Default_Handler                     
000074a7  GROUP0_IRQHandler                   
000074a7  HardFault_Handler                   
000074a7  I2C0_IRQHandler                     
000074a7  I2C1_IRQHandler                     
000074a7  NMI_Handler                         
000074a7  PendSV_Handler                      
000074a7  RTC_IRQHandler                      
000074a7  SPI0_IRQHandler                     
000074a7  SPI1_IRQHandler                     
000074a7  SVC_Handler                         
000074a7  TIMA0_IRQHandler                    
000074a7  TIMA1_IRQHandler                    
000074a7  TIMG0_IRQHandler                    
000074a7  TIMG12_IRQHandler                   
000074a7  TIMG6_IRQHandler                    
000074a7  TIMG7_IRQHandler                    
000074a7  TIMG8_IRQHandler                    
000074a7  UART0_IRQHandler                    
000074a7  UART1_IRQHandler                    
000074a7  UART2_IRQHandler                    
000074a7  UART3_IRQHandler                    
000074af  Reset_Handler                       
000074c5  _system_pre_init                    
000074c8  C$$EXIT                             
000074c9  abort                               
000080c6  asc2_1608                           
000086b6  asc2_0806                           
000088e0  __aeabi_ctype_table_                
000088e0  __aeabi_ctype_table_C               
00008a28  test                                
00008a50  reg                                 
00008ade  hw                                  
00008b7c  __TI_Handler_Table_Base             
00008b88  __TI_Handler_Table_Limit            
00008b90  __TI_CINIT_Base                     
00008ba0  __TI_CINIT_Limit                    
00008ba0  __TI_CINIT_Warm                     
20200000  Serial_RxData                       
20200000  __start___llvm_prf_bits             
20200000  __start___llvm_prf_cnts             
20200000  __stop___llvm_prf_bits              
20200000  __stop___llvm_prf_cnts              
202002f0  sensor                              
202003a0  quat                                
202003b0  Data_Accel                          
202003b6  Data_Gyro                           
202003bc  Data_Pitch                          
202003c0  Data_Roll                           
202003c4  Data_Yaw                            
202003c8  ExISR_Flag                          
202003cc  sensor_timestamp                    
202003d0  sensors                             
202003d2  more                                
202003d4  Motor_Left                          
2020041c  Motor_Right                         
20200490  black                               
202004b0  white                               
202004d7  Data_Tracker_Input                  
202004df  Flag_LED                            
202004e0  Motor                               
202004e8  Data_MotorEncoder                   
202004ec  Data_Motor_TarSpeed                 
202004f0  Data_Tracker_Offset                 
202004f4  __aeabi_errno                       
202004f8  delayTick                           
202004fc  uwTick                              
20200502  Flag_MPU6050_Ready                  
20200505  enable_group1_irq                   
20207e00  __stack                             
20208000  __STACK_END                         
ffffffff  __TI_pprof_out_hndl                 
ffffffff  __TI_prof_data_size                 
ffffffff  __TI_prof_data_start                
ffffffff  __binit__                           
ffffffff  binit                               
UNDEFED   __mpu_init                          
UNDEFED   _system_post_cinit                  
UNDEFED   adc_getValue                        

[299 symbols]
