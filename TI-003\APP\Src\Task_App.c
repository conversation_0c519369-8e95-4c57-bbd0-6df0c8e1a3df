/**
 * @file Task_App.c
 * <AUTHOR> name void Task_OLED(void *para)
 * @brief 任务实现层
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Task_App.h"

#define INDEX 0.2f //转向调试系数
#define DIS_INTERVAL _IQ(0.01f)// 假设相邻传感器间距为0.01米（1cm）

/*Flag*/
extern bool Flag_MPU6050_Ready; //MPU6050是否准备好

/*Data Normal*/
extern uint16_t Data_Gyro[3]; // 陀螺仪原始数据[X轴, Y轴, Z轴] - 角速度传感器输出值
extern uint16_t Data_Accel[3]; // 加速度计原始数据[X轴, Y轴, Z轴] - 线性加速度传感器输出值
extern float Data_Pitch; // 俯仰角(度) - 绕X轴旋转角度，前后倾斜
extern float Data_Roll; // 横滚角(度) - 绕Y轴旋转角度，左右倾斜
extern float Data_Yaw; // 偏航角(度) - 绕Z轴旋转角度，水平转向

/*Data Motor*/
_iq Data_Motor_TarSpeed = _IQ(15); //目标基础速度（调整为15以符合系统需求）
int16_t Data_MotorEncoder[2] = {0}; //电机的编码值
MOTOR_Def_t *Motor[2] = {&Motor_Left, &Motor_Right}; //电机实例

/*Data Tracker*/
uint8_t Data_Tracker_Input[8] = {TRACK_OFF}; //循迹模块的输入值
_iq Data_Tracker_Offset = _IQ(0); //循迹偏差
PID_IQ_Def_t Data_Tracker_PID; //转向环PID

// 灰度传感器模拟值、数字值、归一化值
unsigned short Gray_Anolog[8] = {0};
unsigned short Gray_Normal[8] = {0};
unsigned char Gray_Digtal = 0;
No_MCU_Sensor GraySensor;


/*Test*/
bool Flag_LED = false;

void Task_Key(void *para);
void Task_LED(void *para);
void Task_Motor_PID(void *para);
void Task_Serial(void *para);
void Task_OLED(void *para);
void Task_Tracker(void *para);
void Task_GraySensor(void *para);

void Task_Init(void)
{
    Motor_Start(); //开启电机
    Serial_Init(); //初始化串口
    OLED_Init(); //OLED初始化
    MPU6050_Init(); //MPU6050初始化

    Interrupt_Init(); //中断初始化

    // 新增：初始化灰度传感器
    No_MCU_Ganv_Sensor_Init_Frist(&GraySensor); // 首次初始化
    // 使用预设的黑白阈值初始化（来自gpio_toggle_output.c）
    unsigned short white[8] = {1800,1800,1800,1800,1800,1800,1800,1800};
    unsigned short black[8] = {300,300,300,300,300,300,300,300};
    No_MCU_Ganv_Sensor_Init(&GraySensor, white, black);

    Task_Add("Motor", Task_Motor_PID, 50, NULL, 0);
    Task_Add("Tracker", Task_Tracker, 10, NULL, 1);
    Task_Add("Key", Task_Key, 20, NULL, 5);
    Task_Add("Serial", Task_Serial, 50, NULL, 2);
    Task_Add("LED", Task_LED, 100, NULL, 3);
    Task_Add("OLED", Task_OLED, 50, NULL, 4);
       // 新增：灰度传感器任务（20ms周期，优先级1）
    Task_Add("GraySensor", Task_GraySensor, 10, NULL, 1);
}

//空闲任务函数
void Task_IdleFunction(void)
{
    if (Flag_MPU6050_Ready == true && enable_group1_irq) //检测到标志位后读取
    {
        Flag_MPU6050_Ready = false;
        Read_Quad();
    }
    if (!enable_group1_irq)
    {
        static uint16_t CNT = 0;
        if (++CNT == 5000)
        {
            CNT = 0;
            Read_Quad();
        }
    }
}

//OLED显示 50ms
void Task_OLED(void *para)
{
    // OLED_Printf(0, 16 * 0, 16, "Pitch:%3.2f ", Data_Pitch);
    // OLED_Printf(0, 16 * 1, 16, "Roll:%3.2f ", Data_Roll);
    // OLED_Printf(0, 16 * 2, 16, "Yaw:%3.2f ", Data_Yaw);
    // OLED_Printf(0, 16 * 3, 16, "SysTick:%u ", (uint16_t)(uwTick / 1000));
    OLED_Printf(0, 16 * 0, 8, "[1]:%4.2f  P:%4.1f ", Motor[0]->Motor_PID_Instance.Acutal_Now, Data_Pitch);
    OLED_Printf(0, 16 * 1, 8, "[2]:%4.2f  R:%4.1f ", Motor[1]->Motor_PID_Instance.Acutal_Now, Data_Roll);
    OLED_Printf(0, 16 * 2, 8, "[3]:%4.2f  Y:%4.1f ",  _IQtoF(Data_Tracker_Offset), Data_Yaw);
    OLED_Printf(0, 16 * 3, 8, "[Time]:%4us", (uint16_t)(uwTick / 1000));
}

//按键 20ms
void Task_Key(void *para)
{
    static uint8_t Key_Old = 0;
    uint8_t Key_Temp = Key_Read();
    uint8_t Key_Val = (Key_Temp ^ Key_Old) & Key_Temp;
    Key_Old = Key_Temp;

    if (Key_Val)
    {
        Flag_LED ^= 1;
    }
}

//LED 测试函数 100ms
void Task_LED(void *para)
{
    if (Flag_LED == false)
    {
        LED_BOARD_OFF();
    }
    else
    {
        LED_BOARD_ON();
    }
}

//电机PID调控 50ms
void Task_Motor_PID(void *para)
{
    //获取电机速度
    for (uint8_t i = 0; i < 2; i++)
    {
        Motor_GetSpeed(Motor[i], 50);
    }

    //差速转向控制 - 偏差乘以转向系数
    _iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(INDEX));

    // 左轮：偏右时减速，偏左时加速
    _iq Left_Speed = Data_Motor_TarSpeed - Steering_Adjustment;
    // 右轮：偏右时加速，偏左时减速
    _iq Right_Speed = Data_Motor_TarSpeed + Steering_Adjustment;

    // 设置目标速度
    Motor_Left.Motor_PID_Instance.Target = Left_Speed;
    Motor_Right.Motor_PID_Instance.Target = Right_Speed;

    //PID 计算
    for (uint8_t i = 0; i < 2; i++)
    {
        PID_IQ_Prosc(&Motor[i]->Motor_PID_Instance);
    }

    // 设置电机PWM输出
    for (uint8_t i = 0; i < 2; i++)
    {
        float output = _IQtoF(Motor[i]->Motor_PID_Instance.Out);
        Motor_SetDuty(Motor[i], output);
    }
}

// 灰度传感器读取、计算偏差 20ms
void Task_Tracker(void *para)
{
    const _iq Filter_Value = _IQ(0.7); //滤波系数
    _iq Temp = 0;

     bool res = false;
    if (GraySensor.ok) // 传感器初始化完成
    {
        // 将灰度数字值转换为循迹输入（8位）
        for (uint8_t i = 0; i < 8; i++)
        {
            Data_Tracker_Input[i] = (Gray_Digtal >> i) & 0x01; // 每位对应一个传感器
        }
        // 计算偏差（复用原有加权算法）
        _iq pos_sum = _IQ(0);
        uint8_t cnt = 0;
        for (uint8_t i = 0; i < 8; i++)
        {
            if (Data_Tracker_Input[i] == TRACK_ON)
            {
                _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), DIS_INTERVAL);
                pos_sum += sensor_pos;
                cnt++;
            }
        }
        if (cnt > 0)
        {
            Temp = _IQdiv(pos_sum, _IQ(cnt));
            res = true;
        }
    }

    // 原有滤波逻辑不变
    if (res == true)
    {
        Data_Tracker_Offset = _IQmpy(Temp, Filter_Value) + _IQmpy((_IQ(1) - Filter_Value), Data_Tracker_Offset);
    }
}

//发送显示波形图 50ms
void Task_Serial(void *para)
{
    uint8_t Motor_Param = 0;
    MyPrintf_DMA("%.2f,%.2f,%,2f\r\n",
                 _IQtoF(Motor[Motor_Param]->Motor_PID_Instance.Acutal_Now),
                 _IQtoF(Motor[Motor_Param]->Motor_PID_Instance.Target),
                 _IQtoF(Motor[Motor_Param]->Motor_PID_Instance.Out));

        // 新增：灰度传感器数据输出
    MyPrintf_DMA("Gray:%d,%d,%d,%d,%d,%d,%d,%d\r\n",
                 Gray_Anolog[0], Gray_Anolog[1], Gray_Anolog[2], Gray_Anolog[3],
                 Gray_Anolog[4], Gray_Anolog[5], Gray_Anolog[6], Gray_Anolog[7]);
}

/**
 * @brief 灰度传感器数据读取任务（20ms）
 * 读取模拟值、数字值、归一化值并更新到全局变量
 */
void Task_GraySensor(void *para)
{
    // 调用传感器处理函数（无时基版本，与gpio_toggle_output.c逻辑一致）
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);

    // 获取数字值（二值化结果）
    Gray_Digtal = Get_Digtal_For_User(&GraySensor);

    // 获取模拟值（原始ADC值）
    Get_Anolog_Value(&GraySensor, Gray_Anolog);

    // 获取归一化值（0~4095，基于12位ADC）
    Get_Normalize_For_User(&GraySensor, Gray_Normal);
}